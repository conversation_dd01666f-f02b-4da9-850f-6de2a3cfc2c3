import React from 'react';
import ReactECharts from 'echarts-for-react';
import { colorPrimary, getCountryTimezone, processNumberOrString } from '@/utils/bus';
import { useSearchParams } from '@umijs/max';

export interface Monthly_trends {
  [key: string]: {
    trend: string;
    demond: string;
    strategy: string;
    demand: string;
  }
}
interface IMonthlyTrendsProps {
  data: Monthly_trends
}

const MonthlyTrends: React.FC<IMonthlyTrendsProps> = ({ data }) => {
  const [searchParams] = useSearchParams();
  const country = searchParams.get('country') as string;
  const currentMonth = getCountryTimezone(country, 'M');

  const chartData = Object.entries(data).map(([month, details]) => ({
    month: `${month}月`,
    demond: Number(processNumberOrString(details.demand || details.demond)),
    trend: details.trend,
    strategy: details.strategy,
  }));

  const option = {
    grid: {
      left: 40,
      right: 10,
      bottom: 20,
      top: 25,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: { dataIndex: number }[]) => {
        const dataPoint = chartData[params[0].dataIndex];
        return `
          <strong>${dataPoint.month}</strong><br/>
          需求: ${dataPoint.demond}%<br/>
          趋势: ${dataPoint.trend}
        `;
      },
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.month),
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
    },
    series: [
      {
        name: '市场需求',
        type: 'bar',
        data: chartData.map(item => item.demond),
        itemStyle: {
          color: (params: { dataIndex: number }) => {
            // @ts-ignore
            const month = parseInt(chartData[params.dataIndex].month, 10);
            return month === parseInt(currentMonth, 10) ? colorPrimary : '#1890ff';
          },
        },
      },
    ],
  };

  return <ReactECharts option={option} style={{ height: '100%', width: '100%' }} />;
};

export default MonthlyTrends;