import React, { useState, useEffect } from 'react';
import { Tabs, List, Button, Pagination, Modal, Typography, Spin, Badge, Space, Card, Row, Col } from 'antd';
import styles from './style.less';
import { getMessages } from '@/services/ibidder_api/user';
import WeekMonthAnalysisContent from './components/weekAnalysis';
import WeekStrategyContent from './components/weekStrategy';
import { useSearchParams, useModel } from '@umijs/max';
import { getAvatarByType } from './../bus';
import DayStrategyContent from './components/dayStragegy';
import dayjs from 'dayjs';
import { getDayOfWeek } from '@/utils/bus';

const { TabPane } = Tabs;
const { Title } = Typography;

const job_idMap = {
  'market_report_month': '下月市场分析报告',
  'ads_strategy_week': '下周广告投放策略',
  'ads_strategy_day': '明天广告投放策略',
}

const titleMap = {
  'market_report_month': '月市场分析报告',
  'ads_strategy_week': '周广告投放策略',
  'ads_strategy_day': '日广告投放策略',
}

const roleMap: { [key: string]: string } = {
  strategyAgent: '广告策略师',
  operAgent: '广告优化师',
  marketAgent: '市场分析师',
};

const roleDescMap: { [key: string]: string } = {
  strategyAgent: '广告增长引擎',
  operAgent: 'ROI操盘手',
  marketAgent: '市场雷达',
};

const Todo: React.FC = () => {
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { unreadCount, updateUnreadCount } = useModel('unreadCount');

  const [activeTab, setActiveTab] = useState('0');
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  // const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null]);
  // 分别管理不同类型的 Modal 状态
  const [monthReportModal, setMonthReportModal] = useState<{
    open: boolean;
    data: any;
    title: JSX.Element | null;
    isEdit: boolean;
  }>({
    open: false,
    data: null,
    title: null,
    isEdit: false
  });

  const [weekStrategyModal, setWeekStrategyModal] = useState<{
    open: boolean;
    data: any;
    title: JSX.Element | null;
    isEdit: boolean;
  }>({
    open: false,
    data: null,
    title: null,
    isEdit: false
  });

  const [dayStrategyModal, setDayStrategyModal] = useState<{
    open: boolean;
    data: any;
    title: JSX.Element | null;
    isEdit: boolean;
  }>({
    open: false,
    data: null,
    title: null,
    isEdit: false
  });

  const fetchData = (isUnreadCountFetch = false) => {
    setLoading(true);
    getMessages({
      is_read: isUnreadCountFetch ? 0 : parseInt(activeTab),
      message_type: 5,
      page_no: pageNo,
      page_size: pageSize,
      parent_asin: asin,
      profile_id: profile_id,
    }).then(res => {
      const result = res.data;
      if (activeTab === '0') {
        updateUnreadCount(result.total || 0);
      }
      setData(result.list || []);
      setTotal(result.total || 0);
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    fetchData();
  }, [activeTab, pageNo, pageSize]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setPageNo(1);
  };

  const handlePaginationChange = (page: number, size: number) => {
    setPageNo(page);
    setPageSize(size);
  };

  // 关闭月报告 Modal
  const closeMonthReportModal = () => {
    setMonthReportModal({
      open: false,
      data: null,
      title: null,
      isEdit: false
    });
  };

  // 关闭周策略 Modal
  const closeWeekStrategyModal = () => {
    setWeekStrategyModal({
      open: false,
      data: null,
      title: null,
      isEdit: false
    });
  };

  // 关闭日策略 Modal
  const closeDayStrategyModal = () => {
    setDayStrategyModal({
      open: false,
      data: null,
      title: null,
      isEdit: false
    });
  };

  const handleConfirmAndRefresh = async (refresh: boolean) => {
    // 关闭所有 Modal
    closeMonthReportModal();
    closeWeekStrategyModal();
    closeDayStrategyModal();

    if (refresh) {
      fetchData(true); // Refresh list
    }
  }



  // 获取展示时间
  const getDisplayDate = (data: any) => {
    const { job_id, report_data } = data;
    const realData = report_data?.[job_id];
    let displayDate = ''
    if (!realData) return displayDate
    switch (job_id) {
      case 'ads_strategy_day':
        displayDate = realData.date + getDayOfWeek(realData.date)
        break;
      case 'ads_strategy_week':
        displayDate = realData.start_date + '~' + realData.end_date
        break;
      case 'market_report_month':
        displayDate = realData.start_date + '~' + realData.end_date
        break;
      default:
        displayDate = ''
    }
    return displayDate ? `（${displayDate}）` : ''
  }

  // 获取确认人显示名称
  const getReviewerDisplayName = (reviewerName: string) => {
    return reviewerName === 'sys' ? '超时系统自动审核' : reviewerName;
  }

  const showModal = (item: any) => {
    const title = <Title level={2} style={{ margin: 0 }}>
      {titleMap[item.extra_data.job_id as keyof typeof titleMap]}{getDisplayDate(item.extra_data)}
    </Title>;

    const reportData = item.extra_data;
    const job_id = reportData.job_id;

    // 根据报告类型打开对应的 Modal
    if (job_id === 'market_report_month') {
      setMonthReportModal({
        open: true,
        data: item.extra_data,
        title: title,
        isEdit: item.extra_data.can_edit
      });
    } else if (job_id === 'ads_strategy_week') {
      setWeekStrategyModal({
        open: true,
        data: item.extra_data,
        title: title,
        isEdit: item.extra_data.can_edit
      });
    } else if (job_id === 'ads_strategy_day') {
      setDayStrategyModal({
        open: true,
        data: item.extra_data,
        title: title,
        isEdit: item.extra_data.can_edit
      });
    }
  };

  return (
    <div>
      <Title level={3} style={{ marginTop: '10px' }}>待办事项</Title>
      <Card className="card">
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab={<>待确认{unreadCount > 0 ? <Badge count={unreadCount} style={{ marginLeft: 8 }} /> : ''}</>} key="0" />
          <TabPane tab="已确认" key="1" />
        </Tabs>
        {/* <Flex justify="flex-end" align="center" style={{ marginTop: '1em' }}>
        <RangePicker
          onChange={(dates) => {
            setDateRange(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null]);
            setPageNo(1);
          }}
        />
      </Flex> */}
        <Spin spinning={loading}>
          <List
            itemLayout="horizontal"
            dataSource={data}
            style={{ paddingTop: '1em' }}
            renderItem={item => (
              <List.Item
                key={item.id}
                actions={activeTab === '0' ? [
                  <Button key={`review-btn-${item.id}`} type="primary" ghost onClick={() => showModal(item)}>去审核</Button>] : []}
              >
                <Row style={{ width: '100%' }} justify="space-between" align="middle" gutter={16}>
                  <Col flex={1} >
                    <Space style={{ paddingLeft: '1em' }}>
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        {getAvatarByType(item.extra_data.role)}
                        <span className={styles.todoRole}>
                          <span className={styles.aiWorkType}>{roleMap[item.extra_data.role]}</span>
                          <span className={styles.aiWorkDesc}>{roleDescMap[item.extra_data.role]}</span>
                        </span>
                      </div>
                      <span>
                        {activeTab === '0' ? '请您审核' : '已确认'}
                        <span
                          style={{ color: '#4C6FFF', cursor: 'pointer' }}
                          onClick={() => showModal(item)}
                        >
                          {job_idMap[item.extra_data.job_id as keyof typeof job_idMap]}
                        </span>
                        {getDisplayDate(item.extra_data)}
                      </span>
                    </Space>
                  </Col>

                  <Col style={{width: 250}}>
                    {activeTab === '1' && item.reviewer_name && (
                      <span>
                        {getReviewerDisplayName(item.reviewer_name)}
                      </span>
                    )}
                  </Col>
                  <Col style={{width: 220}}>
                    <span>{dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}（北京）</span>
                  </Col>
                </Row>
              </List.Item>
            )}
          />
        </Spin>
        {total > 0 &&
          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              current={pageNo}
              pageSize={pageSize}
              total={total}
              onChange={handlePaginationChange}
              showSizeChanger showQuickJumper
              pageSizeOptions={['5', '10', '20', '50']}
              showTotal={(total) => `共 ${total} 条`}
            />
          </div>}

      </Card>

      {/* 月报告 Modal */}
      <Modal
        title={monthReportModal.title}
        open={monthReportModal.open}
        onCancel={closeMonthReportModal}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {monthReportModal.data && monthReportModal.data.report_data?.market_report_month && (
          <WeekMonthAnalysisContent
            key={monthReportModal.data.id}
            asins={monthReportModal.data.asins}
            onCancel={handleConfirmAndRefresh}
            onSuccess={() => fetchData(true)}
            job_id={monthReportModal.data.job_id}
            current_time={monthReportModal.data.current_time}
          />
        )}
      </Modal>

      {/* 周策略 Modal */}
      <Modal
        title={weekStrategyModal.title}
        open={weekStrategyModal.open}
        onCancel={closeWeekStrategyModal}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {weekStrategyModal.data && weekStrategyModal.data.report_data?.ads_strategy_week && (
          <WeekStrategyContent
            key={weekStrategyModal.data.id}
            date={weekStrategyModal.data.report_data['ads_strategy_week'].start_date}
            asins={weekStrategyModal.data.asins}
            job_id={weekStrategyModal.data.job_id}
            current_time={weekStrategyModal.data.current_time}
            isCompleteStrategy={true}
            onCancel={handleConfirmAndRefresh}
            onSuccess={() => fetchData(true)}
          />
        )}
      </Modal>

      {/* 日策略 Modal */}
      <Modal
        title={dayStrategyModal.title}
        open={dayStrategyModal.open}
        onCancel={closeDayStrategyModal}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {dayStrategyModal.data && dayStrategyModal.data.report_data?.ads_strategy_day && (
          <DayStrategyContent
            key={dayStrategyModal.data.id}
            onCancel={handleConfirmAndRefresh}
            onSuccess={() => fetchData(true)}
            asins={dayStrategyModal.data.asins}
            job_id={dayStrategyModal.data.job_id}
            current_time={dayStrategyModal.data.current_time}
            date={dayStrategyModal.data.report_data.ads_strategy_day.date}
          />
        )}
      </Modal>
    </div>
  );
};
export default Todo;
