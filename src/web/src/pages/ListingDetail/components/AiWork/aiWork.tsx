import React, { useEffect, useState } from 'react';
import { Tabs, Pagination, Typography, Modal, DatePicker, Card } from 'antd';
import styles from './style.less';
import dayjs from 'dayjs';
import { getAIWorkHistory, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import { useSearchParams } from '@umijs/max';
import DayStrategyContent from './components/dayStragegy';
import WeekStrategyContent from './components/weekStrategy';
import WeekMonthAnalysisContent from './components/weekAnalysis';
import DayStrategyCatContent from './components/dayStrategyCat';
import { getAvatarByType } from '../bus';
import { getDayOfWeek } from '@/utils/bus';
import { Monthly_trends } from '../MonthlyTrends';

const { Title } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const job_idMap: Record<string, string> = {
  'ads_strategy_day': '日广告投放策略',
  'ads_strategy_week': '周广告投放策略',
  'market_report_month': '月市场分析报告',
}

// 定义 setTitle 的参数接口
interface SetTitleParams {
  type: string;
  current_time?: string;
  start_date?: string;
  end_date?: string;
  date?: string;
  noDate?: boolean;
}

const roleMap = {
  all: '全部',
  strategyAgent: '广告策略师',
  operAgent: '广告优化师',
  marketAgent: '市场分析师',
};

const roleDescMap = {
  strategyAgent: '广告增长引擎',
  operAgent: 'ROI操盘手',
  marketAgent: '市场雷达',
};

// AI工作动态数据接口
interface AIWorkItem {
  role: 'strategyAgent' | 'operAgent' | 'marketAgent';
  job_id: string;
  current_country: string;
  current_time: string;
  start_time_local: string;
  currtent_timezone: string;
  bj_time: string;
  es_id: string;
  job_name: string;
  can_edit: boolean;
  profile_id: string;
  asin: string;
  date?: string;
  success: boolean;
  target_week?: string;
  start_time?: string;
  end_time?: string;
  result: {
    ads_strategy_daypart: any;
    ads_strategy_week: any;
    ads_strategy_day: any;
    market_report_week: any;
    market_report_month: any;
    market_trends?: { monthly_trends: Monthly_trends };
    daypart_strategy: any;
    set_campaign_budget?: any;
    set_placement_bidding?: any;
  }
}

const AiWork: React.FC = () => {
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const country = searchParams.get('country') as string;
  const [aiWorkData, setAiWorkData] = useState<AIWorkItem[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [role, setRole] = useState('all');
  const [total, setTotal] = useState(0);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null]);

  // 分别管理不同类型的 Modal 状态
  const [dayStrategyModal, setDayStrategyModal] = useState<{
    open: boolean;
    data: any;
    title: string;
  }>({
    open: false,
    data: null,
    title: ''
  });

  const [weekStrategyModal, setWeekStrategyModal] = useState<{
    open: boolean;
    data: any;
    title: string;
  }>({
    open: false,
    data: null,
    title: ''
  });

  const [weekReportModal, setWeekReportModal] = useState<{
    open: boolean;
    data: any;
    title: string;
  }>({
    open: false,
    data: null,
    title: ''
  });

  const [monthReportModal, setMonthReportModal] = useState<{
    open: boolean;
    data: any;
    title: string;
  }>({
    open: false,
    data: null,
    title: ''
  });

  const [dayStrategyCatModal, setDayStrategyCatModal] = useState<{
    open: boolean;
    data: any;
    title: string;
  }>({
    open: false,
    data: null,
    title: ''
  });



  useEffect(() => {
    getAIWorkHistory<AIWorkItem>({
      asin,
      profile_id,
      page_no: pageNo,
      page_size: pageSize,
      role,
      start_time: dateRange?.[0]?.format('YYYY-MM-DD') || undefined,
      end_time: dateRange?.[1]?.format('YYYY-MM-DD') || undefined,
    }).then(res => {
      setAiWorkData(res.data.list);
      setTotal(res.data.total);
    }).catch(err => {
      console.error(err);
    });
  }, [pageNo, pageSize, role, asin, dateRange]);

  const getTitle = (params: SetTitleParams) => {
    const { type, current_time, start_date, end_date, date, noDate } = params;
    // 获取标题
    switch (type) {
      case 'ads_strategy_day':
        return `日广告投放策略（${dayjs(date).format('YYYY-MM-DD')} ${getDayOfWeek(date)}）`;
      case 'ads_strategy_week':
        if (noDate) {
          return `周广告投放策略`;
        } else {
          const formattedStartDate = dayjs(start_date).format('YYYY-MM-DD');
          const formattedEndDate = dayjs(start_date).add(6, 'day').format('YYYY-MM-DD');
          return `周广告投放策略（${formattedStartDate} ~ ${formattedEndDate}）`;
        }
      case 'market_report_week':
        return `周市场分析报告（${start_date} ~ ${end_date}）`;
      case 'market_report_month':
        return `月市场分析报告（${start_date} ~ ${end_date}）`;
      case 'day_strategy_act':
        return `广告优化调整（${current_time} ${getDayOfWeek(current_time)}）`;
      default:
        return '策略详情';
    }
  }


  /**
   * 显示详情弹框
   * @param params 参数对象
   */
  const showItemDetail = (params: {
    job_id: string;
    esid: string;
    modelType: string;
    asin: string;
    profile_id: string;
    current_time: string;
    target_job_id?: string;
    date?: string;
    start_date?: string;
    end_date?: string;
    role?: 'strategyAgent' | 'operAgent' | 'marketAgent';
    isCompleteStrategy?: boolean;
    can_edit?: boolean;
    noDate?: boolean;
    data?: any;
    country?: string;
    market_trends?: { monthly_trends: Monthly_trends };
  }) => {
    const { job_id, can_edit, esid, country, target_job_id, date, modelType, asin, profile_id, current_time, start_date, end_date, isCompleteStrategy, noDate, data, role, market_trends } = params;

    const title = getTitle({
      type: modelType,
      current_time,
      start_date,
      end_date,
      date,
      noDate
    });

    const modalData = {
      asin,
      profile_id,
      job_id,
      esId: esid,
      current_time,
      target_job_id,
      date,
      isCompleteStrategy,
      start_date,
      end_date,
      isEdit: can_edit,
      data,
      country,
      role,
      market_trends
    };

    // 根据类型打开对应的 Modal
    switch (modelType) {
      case 'ads_strategy_day':
        setDayStrategyModal({
          open: true,
          data: modalData,
          title
        });
        break;
      case 'ads_strategy_week':
        setWeekStrategyModal({
          open: true,
          data: modalData,
          title
        });
        break;
      case 'market_report_week':
        setWeekReportModal({
          open: true,
          data: modalData,
          title
        });
        break;
      case 'market_report_month':
        setMonthReportModal({
          open: true,
          data: modalData,
          title
        });
        break;
      case 'day_strategy_act':
        setDayStrategyCatModal({
          open: true,
          data: modalData,
          title
        });
        break;
      default:
        console.warn('未知的模态框类型:', modelType);
    }
  };

  const getShowDate = (item: AIWorkItem) => {
    if (item.job_id === 'ads_strategy_week') {
      const formattedStartDate = item.result.ads_strategy_week.start_date
      const formattedEndDate = item.result.ads_strategy_week.end_date
      return `${formattedStartDate} ~ ${formattedEndDate}`;
    }
    if (item.job_id === 'market_report_month') {
      const formattedStartDate = item.result.market_report_month.start_date
      const formattedEndDate = item.result.market_report_month.end_date
      return `${formattedStartDate} ~ ${formattedEndDate}`;
    }
    if (item.job_id === 'ads_strategy_day') {
      return item.result.ads_strategy_day?.date;
    }
  }

  const clickPrev = async (item: AIWorkItem) => {
    let prevEsId = item.es_id;
    const parts = item.es_id.split('_');
    if (parts.length > 1) {
      const num = parseInt(parts[1]);
      if (num > 1) {
        prevEsId = `${parts[0]}_${num - 1}`;
      }
    }
    const res = await getRoleAgentDetail<any>({ es_id: prevEsId });
    if (res.code === 200) {
      const data = res.data
      showItemDetail({
        job_id: data.job_id,
        asin: data.asin,
        can_edit: data.can_edit,
        profile_id: data.profile_id,
        current_time: data.current_time,
        esid: prevEsId, // 使用调整后的 esid
        modelType: data.job_id,
        date: data.result[data.job_id]?.date,
        country: country,
        role: data.role,
        data: data.result[data.job_id],
        start_date: data.result[data.job_id]?.start_date,
        end_date: data.result[data.job_id]?.end_date,
        market_trends: data.result.market_trends,
      })
    }
  }

  const clickCur = (item: AIWorkItem) => {
    if (item.job_id === 'market_report_month') {
      showItemDetail({
        job_id: 'market_report_month',
        asin: item.asin,
        can_edit: item.can_edit,
        profile_id: item.profile_id,
        current_time: item.current_time,
        esid: item.es_id,
        modelType: 'market_report_month',
        date: item.date,
        country: country,
        role: item.role,
        data: item.result.market_report_month,
        start_date: item.result.market_report_month.start_date,
        end_date: item.result.market_report_month.end_date,
        market_trends: item.result.market_trends,
      })
    }
    if (item.job_id === 'ads_strategy_week') {
      showItemDetail({
        job_id: 'ads_strategy_week',
        asin: item.asin,
        can_edit: item.can_edit,
        profile_id: item.profile_id,
        current_time: item.current_time,
        esid: item.es_id,
        modelType: 'ads_strategy_week',
        date: item.date,
        country: country,
        role: item.role,
        data: item.result.ads_strategy_week,
        start_date: item.result.ads_strategy_week.start_date,
        end_date: item.result.ads_strategy_week.end_date
      })
    }

    if (item.job_id === 'ads_strategy_day') {
      showItemDetail({
        job_id: 'ads_strategy_day',
        asin: item.asin,
        can_edit: item.can_edit,
        profile_id: item.profile_id,
        current_time: item.current_time,
        country: country,
        role: item.role,
        esid: item.es_id,
        modelType: 'ads_strategy_day',
        date: item.result.ads_strategy_day?.date,
        data: item.result.ads_strategy_day
      })
    }
  }


  const getWorkContent = (item: AIWorkItem) => {
    if (item.es_id.split('_').length > 1 && parseInt(item.es_id.split('_')[1]) > 1) {
      return <div>
        根据您的
        <span className={styles.highlight} onClick={() => { clickPrev(item) }}>修改意见</span>
        重新制定了
        <span className={styles.highlight} onClick={() => { clickCur(item) }}>{job_idMap[item.job_id]}</span>
        （{getShowDate(item)}）
      </div>
    }
    switch (item.job_id) {
      // 日广告投放策略
      case 'ads_strategy_day': {
        const ads_strategy_day = item.result.ads_strategy_day;
        if(!ads_strategy_day) return null
        const maxAdjustment = (ads_strategy_day.bid_adjustment_range?.max * 100).toFixed(0) + '%';
        const minAdjustment = (ads_strategy_day.bid_adjustment_range?.min * 100).toFixed(0) + '%';
        const approachDay = ads_strategy_day.approach === 'balanced' ? '平衡' : ads_strategy_day.approach === 'aggressive' ? '激进' : ads_strategy_day.approach === 'conservative' ? '保守' : ads_strategy_day.approach;
        return (
          <div>
            根据
            <span className={styles.highlight} onClick={() => showItemDetail({
              job_id: item.job_id,
              asin: item.asin,
              can_edit: item.can_edit,
              profile_id: item.profile_id,
              current_time: item.current_time,
              esid: item.es_id,
              modelType: 'ads_strategy_week',
              target_job_id: 'ads_strategy_week',
              role: item.role,
              country: country,
              noDate: true,
            })}
            >本周广告投放策略</span>
            制定了
            <span className={styles.highlight} onClick={() => showItemDetail({
              job_id: 'ads_strategy_day',
              asin: item.asin,
              can_edit: item.can_edit,
              profile_id: item.profile_id,
              current_time: item.current_time,
              country: country,
              role: item.role,
              esid: item.es_id,
              modelType: 'ads_strategy_day',
              date: ads_strategy_day.date,
              data: ads_strategy_day
            })}
            >明天广告投放策略</span>({ads_strategy_day.date} {getDayOfWeek(ads_strategy_day.date)})，
            计划采取<span className={styles.boldItem}>{approachDay}</span>策略，
            总预算为<span className={styles.boldItem}>${ads_strategy_day.day_budget.amount}</span>，
            竞价调整幅度为<span className={styles.boldItem}>{minAdjustment} ~ {maxAdjustment}</span>。
          </div>
        );
      }
      // 周广告投放策略
      case 'ads_strategy_week': {
        const ads_strategy_week = item.result.ads_strategy_week;
        if(!ads_strategy_week) return null
        const approachWeek = ads_strategy_week.approach === 'balanced' ? '平衡' : ads_strategy_week.approach === 'aggressive' ? '激进' : ads_strategy_week.approach === 'conservative' ? '保守' : ads_strategy_week.approach;
        return (
          <div>
            根据
            <span className={styles.highlight} onClick={() => showItemDetail({
              job_id: item.job_id,
              asin: item.asin,
              can_edit: item.can_edit,
              profile_id: item.profile_id,
              current_time: item.current_time,
              esid: item.es_id,
              modelType: 'market_report_week',
              target_job_id: 'market_report_week',
              date: item.start_time,
              start_date: ads_strategy_week.start_date,
              end_date: ads_strategy_week.end_date,
              role: 'marketAgent',
              country: country,
            })}
            >下周市场分析报告</span>
            制定了
            <span className={styles.highlight} onClick={() => showItemDetail({
              job_id: 'ads_strategy_week',
              asin: item.asin,
              can_edit: item.can_edit,
              profile_id: item.profile_id,
              current_time: item.current_time,
              esid: item.es_id,
              modelType: 'ads_strategy_week',
              date: item.date,
              start_date: ads_strategy_week.start_date,
              end_date: ads_strategy_week.end_date,
              role: item.role,
              country: country,
            })}
            >下周广告投放策略</span>({ads_strategy_week.start_date} ~ {ads_strategy_week.end_date})，
            计划采取<span className={styles.boldItem}>{approachWeek}</span>策略，
            主要目标为：<span className={styles.boldItem}>{ads_strategy_week.primary_goal.goal}</span>
          </div>
        );
      }
      // 周市场分析报告
      case 'market_report_week': {
        const market_report_week = item.result.market_report_week;
        if(!market_report_week) return null
        return (
          <div>
            完成了
            <span className={styles.highlight} onClick={() => showItemDetail({
              job_id: item.job_id,
              asin: item.asin,
              can_edit: item.can_edit,
              profile_id: item.profile_id,
              current_time: item.current_time,
              esid: item.es_id,
              role: item.role,
              country: country,
              modelType: 'market_report_week',
              start_date: market_report_week?.start_date,
              end_date: market_report_week?.end_date,
              data: market_report_week
            })}
            >下周市场分析报告</span>({market_report_week?.start_date} ~ {market_report_week?.end_date})，
            {market_report_week?.forecast.market_preview.join(' ')}
          </div>
        );
      }
      // 市场分析师
      case 'market_report_month': {
        const market_report_month = item.result.market_report_month;
        if(!market_report_month) return null
        return (
          <div>
            完成了
            <span className={styles.highlight} onClick={() => showItemDetail({
              job_id: item.job_id,
              asin: item.asin,
              can_edit: item.can_edit,
              profile_id: item.profile_id,
              current_time: item.current_time,
              esid: item.es_id,
              country: country,
              role: item.role,
              modelType: 'market_report_month',
              start_date: market_report_month.start_date,
              end_date: market_report_month.end_date,
              data: market_report_month,
              market_trends: item.result.market_trends
            })}
            >下月市场分析报告</span>({market_report_month.start_date} ~ {market_report_month.end_date})，
            {market_report_month.forecast.market_preview.join(' ')}
          </div>
        );
      }
      // 广告优化师
      case 'day_strategy_act': {
        // 获取预算调整和位置竞价的结果
        const campaignBudgetResult = item.result.set_campaign_budget?.set_campaign_budget || '';
        const placementBiddingResult = item.result.set_placement_bidding?.set_placement_bidding || '';
        const daypart_strategy = item.result.daypart_strategy;
        const ads_strategy_daypart = item.result.ads_strategy_daypart;
        // 格式化当前日期，使用current_time的日期部分或者今天的日期
        const displayDate = item.current_time ?
          dayjs(item.current_time).format('YYYY-MM-DD') :
          dayjs().format('YYYY-MM-DD');

        // 判断current_time是否为0点
        const isZeroHour = item.current_time ?
          dayjs(item.current_time).format('HH') === '00' :
          false;
        return (
          isZeroHour ?
            <div>
              根据
              <span className={styles.highlight} onClick={() => showItemDetail({
                job_id: 'ads_strategy_day',
                asin: item.asin,
                can_edit: item.can_edit,
                profile_id: item.profile_id,
                current_time: item.current_time,
                esid: item.es_id,
                date: displayDate,
                modelType: 'ads_strategy_day',
                target_job_id: 'ads_strategy_day',
                role: 'strategyAgent',
                country: country,
              })}
              >日广告投放策略</span>
              （{displayDate} {getDayOfWeek(displayDate)}）
              {campaignBudgetResult ? `，广告预算：${campaignBudgetResult}` : ''}
              {placementBiddingResult ? `，位置竞价：${placementBiddingResult}` : ''}
            </div>
            :
            (
              daypart_strategy ?
                <div>
                  根据今天广告投放表现调整了
                  <span className={styles.highlight} onClick={() => showItemDetail({
                    job_id: 'day_strategy_act',
                    asin: item.asin,
                    can_edit: item.can_edit,
                    profile_id: item.profile_id,
                    current_time: item.current_time,
                    esid: item.es_id,
                    modelType: 'day_strategy_act',
                    role: item.role,
                    country: country,
                  })}
                  >广告优化调整</span>（{item.current_time}），
                  {daypart_strategy?.daily_adjustment_proposal?.overall_rationale}
                </div>
                :
                <div>
                  根据今天广告投放表现调整了
                  <span className={styles.highlight} onClick={() => showItemDetail({
                    job_id: 'day_strategy_act',
                    asin: item.asin,
                    can_edit: item.can_edit,
                    profile_id: item.profile_id,
                    current_time: item.current_time,
                    esid: item.es_id,
                    modelType: 'day_strategy_act',
                    role: item.role,
                    country: country,
                  })}
                  >广告优化调整</span>（{item.current_time}），
                  {ads_strategy_daypart?.daily_adjustment_proposal?.overall_rationale}
                </div>
            )

        );
      }
      default:
        return null;
    }
  };





  return <div className={styles.container}>
    {/* AI工作动态板块 */}
    <Title level={3} className={styles.title}>AI 工作动态</Title>

    <Card className="card">
      <div className={styles.aiWorkContainer}>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={(dates) => {
              setDateRange(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null]);
              setPageNo(1);
            }}
          />
        </div>
        <Tabs
          activeKey={role}
          onChange={(key) => {
            setRole(key);
            setPageNo(1);
          }}
          className={styles.aiWorkTabs}
        >
          {(Object.keys(roleMap) as Array<keyof typeof roleMap>).map(key => (
            <TabPane tab={roleMap[key]} key={key} />
          ))}
        </Tabs>

        <div className={styles.aiWorkList}>
          {aiWorkData.map((item, index) => (
            <div key={index} className={styles.aiWorkItem}>
              {getAvatarByType(item.role)}
              <div className={styles.aiWorkContent}>
                <div className={styles.aiWorkRole}>
                <span className={styles.aiWorkType}>{roleMap[item.role]}</span>
                  <span className={styles.aiWorkDesc}>{roleDescMap[item.role]}</span>
                </div>
                <div className={styles.aiWorkMainContent}>
                  {getWorkContent(item)}
                </div>
                <span className={styles.aiWorkTimestamp}>
                  {item.start_time_local}（{item.current_country.toLocaleUpperCase()}）
                  <br />
                  {item.bj_time}（北京）
                </span>
              </div>
            </div>
          ))}
        </div>

        {total > pageSize && (
          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              current={pageNo}
              pageSize={pageSize}
              total={total}
              onChange={(page, pageSize) => {
                setPageNo(page);
                setPageSize(pageSize);
              }}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `共 ${total} 条记录`}
            />
          </div>
        )}
      </div>
    </Card>

    {/* 日策略 Modal */}
    <Modal
      title={<Title level={2} style={{ margin: 0 }}>{dayStrategyModal.title}</Title>}
      open={dayStrategyModal.open}
      onCancel={() => setDayStrategyModal({ open: false, data: null, title: '' })}
      width="90%"
      footer={null}
      destroyOnClose
      className='report-modal'
    >
      {dayStrategyModal.data && (
        <DayStrategyContent
          key={dayStrategyModal.data.esId + dayStrategyModal.data.current_time}
          job_id={dayStrategyModal.data.job_id}
          current_time={dayStrategyModal.data.current_time}
          target_job_id={dayStrategyModal.data.target_job_id}
          date={dayStrategyModal.data.date}
          onCancel={() => setDayStrategyModal({ open: false, data: null, title: '' })}
          onSuccess={() => {}}
        />
      )}
    </Modal>

    {/* 周策略 Modal */}
    <Modal
      title={<Title level={2} style={{ margin: 0 }}>{weekStrategyModal.title}</Title>}
      open={weekStrategyModal.open}
      onCancel={() => setWeekStrategyModal({ open: false, data: null, title: '' })}
      width="90%"
      footer={null}
      destroyOnClose
      className='report-modal'
    >
      {weekStrategyModal.data && (
        <WeekStrategyContent
          key={weekStrategyModal.data.esId + weekStrategyModal.data.current_time}
          job_id={weekStrategyModal.data.job_id}
          current_time={weekStrategyModal.data.current_time}
          target_job_id={weekStrategyModal.data.target_job_id}
          date={weekStrategyModal.data.date}
          isCompleteStrategy={weekStrategyModal.data.isCompleteStrategy}
          onCancel={() => setWeekStrategyModal({ open: false, data: null, title: '' })}
          onSuccess={() => {}}
        />
      )}
    </Modal>

    {/* 周报告 Modal */}
    <Modal
      title={<Title level={2} style={{ margin: 0 }}>{weekReportModal.title}</Title>}
      open={weekReportModal.open}
      onCancel={() => setWeekReportModal({ open: false, data: null, title: '' })}
      width="90%"
      footer={null}
      destroyOnClose
      className='report-modal'
    >
      {weekReportModal.data && (
        <WeekMonthAnalysisContent
          key={weekReportModal.data.esId + weekReportModal.data.current_time}
          job_id={weekReportModal.data.job_id}
          current_time={weekReportModal.data.current_time}
          target_job_id={weekReportModal.data.target_job_id}
          onSuccess={() => {}}
          onCancel={() => setWeekReportModal({ open: false, data: null, title: '' })}
        />
      )}
    </Modal>

    {/* 月报告 Modal */}
    <Modal
      title={<Title level={2} style={{ margin: 0 }}>{monthReportModal.title}</Title>}
      open={monthReportModal.open}
      onCancel={() => setMonthReportModal({ open: false, data: null, title: '' })}
      width="90%"
      footer={null}
      destroyOnClose
      className='report-modal'
    >
      {monthReportModal.data && (
        <WeekMonthAnalysisContent
          key={monthReportModal.data.esId + monthReportModal.data.current_time}
          job_id={monthReportModal.data.job_id}
          current_time={monthReportModal.data.current_time}
          target_job_id={monthReportModal.data.target_job_id}
          onCancel={() => setMonthReportModal({ open: false, data: null, title: '' })}
          onSuccess={() => {}}
        />
      )}
    </Modal>

    {/* 日策略分类 Modal */}
    <Modal
      title={<Title level={2} style={{ margin: 0 }}>{dayStrategyCatModal.title}</Title>}
      open={dayStrategyCatModal.open}
      onCancel={() => setDayStrategyCatModal({ open: false, data: null, title: '' })}
      width="90%"
      footer={null}
      destroyOnClose
      className='report-modal'
    >
      {dayStrategyCatModal.data && (
        <DayStrategyCatContent
          key={dayStrategyCatModal.data.esId + dayStrategyCatModal.data.current_time}

          job_id={dayStrategyCatModal.data.job_id}
          current_time={dayStrategyCatModal.data.current_time}
          target_job_id={dayStrategyCatModal.data.target_job_id}

        />
      )}
    </Modal>
  </div>;
};

export default AiWork;