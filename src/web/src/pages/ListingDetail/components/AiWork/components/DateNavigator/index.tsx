import React, { useState, useEffect, useRef } from 'react';
import { Button, Space, message } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { useSearchParams } from '@umijs/max';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);
import styles from './index.less';
import { getAllReportDates, getAdStrategy } from '@/services/ibidder_api/operation';

interface DateNavigatorProps {
  initialDate?: string;
  onStrategyDataUpdate: (data: any) => void;
  job_id?: string;
  dateType: 'single' | 'range'; // 新增：区分单日期和区间日期
}

// API 返回的日期数据类型
interface DateItem {
  start_time: string;
  end_time: string;
  target_week: string;
}

// API 响应类型
interface GetAllReportDatesResponse {
  code: number;
  message?: string;
  data: {
    date: DateItem[];
    asin: string;
    profile_id: string;
    job_id: string;
  };
}

/* 日期导航器组件 根据传入的 country 获取时区对应的日期 */
const DateNavigator: React.FC<DateNavigatorProps> = ({
  onStrategyDataUpdate,
  initialDate,
  job_id,
  dateType = 'single' // 默认为单日期
}) => {
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;

  const scrollWrapperRef = useRef<HTMLDivElement>(null);
  const [selectedDate, setSelectedDate] = useState(initialDate || '');
  const [dateItems, setDateItems] = useState<DateItem[]>([]);
  const [showNavigationButtons, setShowNavigationButtons] = useState(false);

  // 检查是否需要显示导航按钮
  const checkIfNavigationNeeded = () => {
    if (scrollWrapperRef.current) {
      const wrapper = scrollWrapperRef.current;
      const needsNavigation = wrapper.scrollWidth > wrapper.clientWidth;
      setShowNavigationButtons(needsNavigation);
    }
  };

  // 获取当前显示的日期数据
  const loadDateData = async () => {
    try {
      const res = await getAllReportDates({
        asin,
        job_id: job_id + '',
        profile_id,
      }) as GetAllReportDatesResponse;

      if (res.code === 200 && res.data && res.data.date) {
        const items = res.data.date;
        setDateItems(items);
      }
    } catch (error) {
      console.error('获取日期数据失败:', error);
    }
  };

  useEffect(() => {
    // 加载日期数据
    loadDateData();
  }, [asin, profile_id, job_id, dateType]);

  // 在数据加载后检查是否需要导航按钮
  useEffect(() => {
    if (dateItems.length > 0) {
      // 使用 setTimeout 确保 DOM 已经渲染完成
      setTimeout(() => {
        checkIfNavigationNeeded();
      }, 0);
    }
  }, [dateItems]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      checkIfNavigationNeeded();
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handlePrev = () => {
    // 每次向前滚动可见区域的一半
    if (scrollWrapperRef.current) {
      const scrollAmount = scrollWrapperRef.current.offsetWidth / 2;
      scrollWrapperRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const handleNext = () => {
    // 每次向后滚动可见区域的一半
    if (scrollWrapperRef.current) {
      const scrollAmount = scrollWrapperRef.current.offsetWidth / 2;
      scrollWrapperRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const handleDateClick = async (selectedDate: string) => {
    setSelectedDate(selectedDate);

    if (!asin || !profile_id) {
      message.error('缺少必要参数 asin 或 profile_id');
      return;
    }

    // 根据显示的日期字符串找到对应的原始数据项
    const selectedIndex = dateItems.findIndex(item => item.target_week === selectedDate);
    if (selectedIndex === -1 || !dateItems[selectedIndex]) {
      message.error('无法找到对应的日期数据');
      return;
    }

    const selectedDateItem = dateItems[selectedIndex];
    // 使用 start_time 作为 API 调用的日期参数
    const apiDate = selectedDateItem.start_time;

    try {
      const res: any = await getAdStrategy({
        date: apiDate,
        asin,
        profile_id,
        job_id,
      });
      if (res.code === 200 && res.data ) {
        onStrategyDataUpdate(res.data);
      } else {
        // message.error(res.message || '获取策略数据失败');
        onStrategyDataUpdate(null);
      }
    } catch (error) {
      // console.error('获取策略数据失败:', error);
      // message.error('获取数据失败，请重试');
      onStrategyDataUpdate(null);
    }
  };

  return (
    <div className={`${styles.dateNavigatorContainer} ${showNavigationButtons ? styles.hasNavigation : ''}`}>
      {showNavigationButtons && (
        <Button
          icon={<LeftOutlined />}
          type="text"
          onClick={handlePrev}
          size="small"
        />
      )}
      <div className={styles.dateButtonsWrapper} ref={scrollWrapperRef}>
        <Space size='middle'>
          {dateItems.map((item) => (
            <Button
              key={item.start_time}
              color={
                job_id === 'market_report_month'
                  ? (dayjs(selectedDate).isSame(dayjs(item.start_time), 'month') ? 'primary' : 'default')
                  : (item.start_time === selectedDate ? 'primary' : 'default')
              }
              onClick={() => handleDateClick(item.start_time)}
              size='small'
              className={styles.dateButton}
            >
              {
                job_id === 'market_report_month'
                  ? dayjs(item.start_time).format('YYYY-MM月')
                  : dateType === 'single'
                    ? item.start_time
                    : `${item.start_time} ~ ${item.end_time}`
              }
            </Button>
          ))}
        </Space>
      </div>
      {showNavigationButtons && (
        <Button
          icon={<RightOutlined />}
          type="text"
          onClick={handleNext}
          size="small"
        />
      )}
    </div>
  );
};

export default DateNavigator;
