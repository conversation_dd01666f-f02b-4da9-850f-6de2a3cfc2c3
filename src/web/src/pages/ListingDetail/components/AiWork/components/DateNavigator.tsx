import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { Button, Space, message } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { useSearchParams } from '@umijs/max';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);
import styles from './DateNavigator.less';
import { getAllReportDates, getAdStrategy } from '@/services/ibidder_api/operation';
import { getDayOfWeek } from '@/utils/bus';

interface DateNavigatorProps {
  initialDate?: string;
  onStrategyDataUpdate: (data: any) => void;
  setLoading: (loading: boolean) => void;
  job_id?: string;
  dateType: 'single' | 'range'; // 新增：区分单日期和区间日期
}

// API 返回的日期数据类型
interface DateItem {
  start_time: string;
  end_time: string;
  target_week: string;
}

// API 响应类型
interface GetAllReportDatesResponse {
  code: number;
  message?: string;
  data: {
    date: DateItem[];
    asin: string;
    profile_id: string;
    job_id: string;
  };
}

/* 日期导航器组件 根据传入的 country 获取时区对应的日期 */
const DateNavigator: React.FC<DateNavigatorProps> = (props) => {
  const { onStrategyDataUpdate, initialDate, job_id, setLoading, dateType = 'single', } = props
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const scrollWrapperRef = useRef<HTMLDivElement>(null);
  const [selectedDate, setSelectedDate] = useState('');
  const [dateItems, setDateItems] = useState<DateItem[]>([]);
  const [showNavButtons, setShowNavButtons] = useState(false);
  // 获取当前显示的日期数据
  const loadDateData = async () => {
    try {
      const res = await getAllReportDates({
        asin,
        job_id: job_id + '',
        profile_id,
      }) as GetAllReportDatesResponse;

      if (res.code === 200 && res.data && res.data.date) {
        const items = res.data.date;
        setDateItems(items);
      }
    } catch (error) {
      console.error('获取日期数据失败:', error);
    }
  };

  useEffect(() => {
    if (initialDate) {
      setSelectedDate(initialDate);
    }
  }, [initialDate]);


  useEffect(() => {
    // 加载日期数据
    loadDateData();
  }, [asin, profile_id, job_id, dateType]);

  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (scrollWrapperRef.current) {
        const { scrollWidth, clientWidth } = scrollWrapperRef.current;
        setShowNavButtons(scrollWidth > clientWidth);
      }
    };

    checkOverflow();

    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [dateItems]);

  const handlePrev = () => {
    // 每次向前滚动可见区域的一半
    if (scrollWrapperRef.current) {
      const scrollAmount = scrollWrapperRef.current.offsetWidth / 2;
      scrollWrapperRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const handleNext = () => {
    // 每次向后滚动可见区域的一半
    if (scrollWrapperRef.current) {
      const scrollAmount = scrollWrapperRef.current.offsetWidth / 2;
      scrollWrapperRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const handleDateClick = async (selectedDate: string) => {
    setSelectedDate(selectedDate);

    if (!asin || !profile_id) {
      message.error('缺少必要参数 asin 或 profile_id');
      return;
    }

    // 根据显示的日期字符串找到对应的原始数据项
    const selectedIndex = dateItems.findIndex(item => item.target_week === selectedDate);
    if (selectedIndex === -1 || !dateItems[selectedIndex]) {
      message.error('无法找到对应的日期数据');
      return;
    }

    const selectedDateItem = dateItems[selectedIndex];
    // 使用 start_time 作为 API 调用的日期参数
    const apiDate = selectedDateItem.start_time;
    setLoading(true);
    try {
      const res: any = await getAdStrategy({
        date: apiDate,
        asin,
        profile_id,
        job_id,
      });
      setLoading(false);
      if (res.code === 200 && res.data) {
        onStrategyDataUpdate(res.data);
      } else {
        // message.error(res.message || '获取策略数据失败');
        onStrategyDataUpdate(null);
      }
    } catch (error) {
      setLoading(false);
      // console.error('获取策略数据失败:', error);
      // message.error('获取数据失败，请重试');
      onStrategyDataUpdate(null);
    }
  };

  return (
    <div className={styles.dateNavigatorContainer}>
      {showNavButtons && (
        <Button icon={<LeftOutlined />} type="text" onClick={handlePrev} size="small" />
      )}
      <div className={styles.dateButtonsWrapper} ref={scrollWrapperRef}>
        <Space size="middle">
          {dateItems.map((item) => (
            <Button
              key={item.start_time}
              type={
                job_id === 'market_report_month'
                  ? (dayjs(selectedDate).isSame(dayjs(item.start_time), 'month') ? 'primary' : 'default')
                  : (item.start_time === selectedDate ? 'primary' : 'default')
              }
              onClick={() => handleDateClick(item.start_time)}
              size='small'
              className={styles.dateButton}
            >
              {
                job_id === 'market_report_month'
                  ? dayjs(item.start_time).format('YYYY-MM月')
                  : dateType === 'single'
                    ? `${item.start_time} ${getDayOfWeek(item.start_time)}`
                    : `${item.start_time} ~ ${item.end_time}`
              }
            </Button>
          ))}
        </Space>
      </div>
      {showNavButtons && (
        <Button icon={<RightOutlined />} type="text" onClick={handleNext} size="small" />
      )}
    </div>
  );
};

export default DateNavigator;
